# 分布式系统实验

本项目包含两个主要实验：

## 第一项：分布式实时电商推荐系统 (50分)
基于Kafka和Flink构建的实时推荐系统，包含：
- 3节点Kafka集群配置
- Flink流处理引擎配置
- 实时推荐算法实现
- 消息源软件模拟用户行为

## 第二项：Raft一致性协议 (40分)
Go语言实现的Raft共识算法，包含：
- 领导者选举机制
- 日志复制过程
- 状态持久化
- 日志压缩和快照

## 环境要求
- 操作系统：Linux (Ubuntu 20.04 或 CentOS 7)
- 硬件：至少2核CPU，8GB内存，50GB存储
- 软件：Apache Kafka 3.6.0, Apache Flink 1.17, Go语言环境

## 项目结构
```
distributed_system_experiment/
├── lab01-kafka-flink/          # 第一项实验
│   ├── kafka-cluster/          # Kafka集群配置
│   ├── flink-cluster/          # Flink集群配置
│   ├── recommendation-system/  # 推荐系统实现
│   └── message-source/         # 消息源软件
├── lab02-raft/                 # 第二项实验
│   ├── src/                    # Raft实现源码
│   ├── test/                   # 测试用例
│   └── docs/                   # 文档
└── docs/                       # 实验报告和文档
```

## 快速开始

### 第一项实验 - Kafka + Flink 推荐系统

1. **环境配置**
```bash
cd lab01-kafka-flink
./setup-environment.sh
```

2. **启动Kafka集群**
```bash
cd kafka-cluster
./start-kafka-cluster.sh
```

3. **启动Flink集群**
```bash
cd flink-cluster
./start-flink-cluster.sh
```

4. **运行推荐系统**
```bash
cd recommendation-system
./run-recommendation-system.sh
```

5. **启动消息源软件**
```bash
cd message-source
./run-message-source.sh
```

### 第二项实验 - Raft协议

1. **编译和测试**
```bash
cd lab02-raft
go build ./...
go test -v ./...
```

2. **运行特定测试**
```bash
# 测试选举
go test -run TestInitialElection2A

# 测试日志复制
go test -run TestBasicAgree2B

# 测试持久化
go test -run TestPersist12C

# 测试快照
go test -run TestSnapshotBasic2D
```

## 实验报告

详细的实验报告和截图请参考 `docs/` 目录下的文档。

## 注意事项

1. 确保所有节点之间网络连通
2. 配置正确的主机名和IP地址
3. 检查防火墙设置
4. 确保有足够的内存和存储空间
5. 按照实验要求的版本安装软件

## 联系信息

如有问题，请参考实验文档或联系助教。

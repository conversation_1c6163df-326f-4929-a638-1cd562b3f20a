#!/bin/bash

# 推荐系统运行脚本

set -e

echo "=== 启动分布式实时电商推荐系统 ==="

# 检查环境变量
if [ -z "$FLINK_HOME" ]; then
    echo "错误: FLINK_HOME环境变量未设置"
    exit 1
fi

if [ -z "$KAFKA_HOME" ]; then
    echo "错误: KAFKA_HOME环境变量未设置"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 编译项目
echo "编译推荐系统项目..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "项目编译失败"
    exit 1
fi

# 检查Kafka是否运行
echo "检查Kafka服务状态..."
if ! pgrep -f "kafka.Kafka" > /dev/null; then
    echo "错误: Kafka服务未运行，请先启动Kafka集群"
    exit 1
fi

# 检查Flink是否运行
echo "检查Flink服务状态..."
if ! pgrep -f "StandaloneSessionClusterEntrypoint" > /dev/null; then
    echo "错误: Flink JobManager未运行，请先启动Flink集群"
    exit 1
fi

# 创建Kafka主题
echo "创建Kafka主题..."

# 商品事件主题
$KAFKA_HOME/bin/kafka-topics.sh --create \
    --topic product-events \
    --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092 \
    --partitions 3 \
    --replication-factor 3 \
    --if-not-exists

# 用户行为事件主题
$KAFKA_HOME/bin/kafka-topics.sh --create \
    --topic user-behavior-events \
    --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092 \
    --partitions 3 \
    --replication-factor 3 \
    --if-not-exists

# 推荐结果主题
$KAFKA_HOME/bin/kafka-topics.sh --create \
    --topic recommendation-results \
    --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092 \
    --partitions 3 \
    --replication-factor 3 \
    --if-not-exists

echo "Kafka主题创建完成"

# 列出所有主题
echo "当前Kafka主题列表:"
$KAFKA_HOME/bin/kafka-topics.sh --list --bootstrap-server hadoop01:9092,hadoop02:9092,hadoop03:9092

# 提交Flink作业
echo "提交推荐系统作业到Flink集群..."
JAR_FILE="target/recommendation-system-1.0-SNAPSHOT.jar"

if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    exit 1
fi

# 提交作业
$FLINK_HOME/bin/flink run \
    --class com.distributed.system.RecommendationSystemJob \
    --parallelism 3 \
    $JAR_FILE

echo "推荐系统作业已提交到Flink集群"
echo "可以通过以下方式监控作业:"
echo "  Flink Web UI: http://hadoop01:8081"
echo "  查看作业列表: $FLINK_HOME/bin/flink list"
echo "  查看日志: tail -f logs/recommendation-system.log"

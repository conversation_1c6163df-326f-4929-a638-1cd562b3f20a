package com.distributed.system;

import com.distributed.system.model.Product;
import com.distributed.system.model.Recommendation;
import com.distributed.system.model.UserBehavior;
import com.distributed.system.processor.RecommendationProcessor;
import com.distributed.system.utils.JsonUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.PrintSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * 分布式实时电商推荐系统主作业
 */
public class RecommendationSystemJob {
    
    private static final Logger LOG = LoggerFactory.getLogger(RecommendationSystemJob.class);
    
    // Kafka配置
    private static final String KAFKA_BROKERS = "hadoop01:9092,hadoop02:9092,hadoop03:9092";
    private static final String PRODUCT_TOPIC = "product-events";
    private static final String USER_BEHAVIOR_TOPIC = "user-behavior-events";
    private static final String RECOMMENDATION_TOPIC = "recommendation-results";
    private static final String CONSUMER_GROUP = "recommendation-system-group";
    
    public static void main(String[] args) throws Exception {
        
        LOG.info("启动分布式实时电商推荐系统...");
        
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(3);
        
        // 启用检查点
        env.enableCheckpointing(60000); // 60秒
        
        // 配置Kafka源 - 商品事件
        KafkaSource<String> productSource = KafkaSource.<String>builder()
                .setBootstrapServers(KAFKA_BROKERS)
                .setTopics(PRODUCT_TOPIC)
                .setGroupId(CONSUMER_GROUP + "-product")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        // 配置Kafka源 - 用户行为事件
        KafkaSource<String> userBehaviorSource = KafkaSource.<String>builder()
                .setBootstrapServers(KAFKA_BROKERS)
                .setTopics(USER_BEHAVIOR_TOPIC)
                .setGroupId(CONSUMER_GROUP + "-behavior")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();
        
        // 配置Kafka输出 - 推荐结果
        KafkaSink<String> recommendationSink = KafkaSink.<String>builder()
                .setBootstrapServers(KAFKA_BROKERS)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(RECOMMENDATION_TOPIC)
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build())
                .build();
        
        // 处理商品事件流
        DataStream<String> productStream = env.fromSource(
                productSource,
                WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(5)),
                "Product Source"
        );
        
        DataStream<Product> products = productStream
                .map(new MapFunction<String, Product>() {
                    @Override
                    public Product map(String value) throws Exception {
                        Product product = JsonUtils.fromJson(value, Product.class);
                        if (product != null) {
                            LOG.info("接收到商品事件: {}", product);
                        }
                        return product;
                    }
                })
                .filter(product -> product != null);
        
        // 处理用户行为事件流
        DataStream<String> userBehaviorStream = env.fromSource(
                userBehaviorSource,
                WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(5)),
                "User Behavior Source"
        );
        
        DataStream<UserBehavior> userBehaviors = userBehaviorStream
                .map(new MapFunction<String, UserBehavior>() {
                    @Override
                    public UserBehavior map(String value) throws Exception {
                        UserBehavior behavior = JsonUtils.fromJson(value, UserBehavior.class);
                        if (behavior != null) {
                            LOG.info("接收到用户行为事件: {}", behavior);
                        }
                        return behavior;
                    }
                })
                .filter(behavior -> behavior != null);
        
        // 生成推荐
        DataStream<Recommendation> recommendations = userBehaviors
                .keyBy(UserBehavior::getUserId)
                .process(new RecommendationProcessor());
        
        // 输出推荐结果到Kafka
        DataStream<String> recommendationJson = recommendations
                .map(new MapFunction<Recommendation, String>() {
                    @Override
                    public String map(Recommendation recommendation) throws Exception {
                        String json = JsonUtils.toJson(recommendation);
                        LOG.info("生成推荐结果: {}", json);
                        return json;
                    }
                })
                .filter(json -> json != null);
        
        // 发送到Kafka
        recommendationJson.sinkTo(recommendationSink);
        
        // 同时打印到控制台（用于调试）
        recommendations.addSink(new PrintSinkFunction<>("推荐结果", false));
        
        // 打印商品信息（用于调试）
        products.addSink(new PrintSinkFunction<>("商品信息", false));
        
        // 打印用户行为（用于调试）
        userBehaviors.addSink(new PrintSinkFunction<>("用户行为", false));
        
        LOG.info("推荐系统作业配置完成，开始执行...");
        
        // 执行作业
        env.execute("Distributed Real-time E-commerce Recommendation System");
    }
}

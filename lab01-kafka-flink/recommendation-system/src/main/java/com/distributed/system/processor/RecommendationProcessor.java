package com.distributed.system.processor;

//import com.distributed.system.model.Product;
import com.distributed.system.model.Recommendation;
import com.distributed.system.model.UserBehavior;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
//import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐算法处理器
 * 基于用户行为数据生成实时推荐
 */
public class RecommendationProcessor extends KeyedProcessFunction<String, UserBehavior, Recommendation> {
    
    private static final Logger LOG = LoggerFactory.getLogger(RecommendationProcessor.class);
    
    // 用户行为历史状态
    private transient MapState<String, Integer> userProductInteractions;
    
    // 用户类别偏好状态
    private transient MapState<String, Double> userCategoryPreferences;
    
    // 最后推荐时间状态
    private transient ValueState<Long> lastRecommendationTime;
    
    // 推荐间隔（毫秒）
    private static final long RECOMMENDATION_INTERVAL = 30000; // 30秒
    
    // 推荐商品数量
    private static final int RECOMMENDATION_COUNT = 5;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        MapStateDescriptor<String, Integer> interactionDescriptor = new MapStateDescriptor<>(
                "user-product-interactions",
                TypeInformation.of(String.class),
                TypeInformation.of(Integer.class)
        );
        userProductInteractions = getRuntimeContext().getMapState(interactionDescriptor);
        
        MapStateDescriptor<String, Double> preferenceDescriptor = new MapStateDescriptor<>(
                "user-category-preferences",
                TypeInformation.of(String.class),
                TypeInformation.of(Double.class)
        );
        userCategoryPreferences = getRuntimeContext().getMapState(preferenceDescriptor);
        
        ValueStateDescriptor<Long> timeDescriptor = new ValueStateDescriptor<>(
                "last-recommendation-time",
                TypeInformation.of(Long.class)
        );
        lastRecommendationTime = getRuntimeContext().getState(timeDescriptor);
    }
    
    @Override
    public void processElement(UserBehavior behavior, Context context, Collector<Recommendation> collector) 
            throws Exception {
        
        LOG.info("处理用户行为: {}", behavior);
        
        // 更新用户-商品交互次数
        updateUserProductInteractions(behavior);
        
        // 更新用户类别偏好
        updateUserCategoryPreferences(behavior);
        
        // 检查是否需要生成推荐
        Long lastTime = lastRecommendationTime.value();
        long currentTime = System.currentTimeMillis();
        
        if (lastTime == null || (currentTime - lastTime) > RECOMMENDATION_INTERVAL) {
            // 生成推荐
            Recommendation recommendation = generateRecommendation(behavior.getUserId(), behavior.getSessionId());
            if (recommendation != null && !recommendation.getRecommendedProducts().isEmpty()) {
                collector.collect(recommendation);
                lastRecommendationTime.update(currentTime);
                LOG.info("生成推荐: {}", recommendation);
            }
        }
    }
    
    /**
     * 更新用户-商品交互次数
     */
    private void updateUserProductInteractions(UserBehavior behavior) throws Exception {
        String productId = behavior.getProductId();
        Integer currentCount = userProductInteractions.get(productId);
        
        // 根据行为类型设置权重
        int weight = getWeightByBehaviorType(behavior.getBehaviorType());
        int newCount = (currentCount == null ? 0 : currentCount) + weight;
        
        userProductInteractions.put(productId, newCount);
    }
    
    /**
     * 更新用户类别偏好
     */
    private void updateUserCategoryPreferences(UserBehavior behavior) throws Exception {
        String category = behavior.getCategory();
        if (category != null) {
            Double currentPreference = userCategoryPreferences.get(category);
            double weight = getWeightByBehaviorType(behavior.getBehaviorType()) * 0.1;
            double newPreference = (currentPreference == null ? 0.0 : currentPreference) + weight;
            
            userCategoryPreferences.put(category, newPreference);
        }
    }
    
    /**
     * 根据行为类型获取权重
     */
    private int getWeightByBehaviorType(String behaviorType) {
        switch (behaviorType.toLowerCase()) {
            case "purchase":
                return 10;
            case "add_to_cart":
                return 5;
            case "click":
                return 2;
            case "view":
                return 1;
            default:
                return 1;
        }
    }
    
    /**
     * 生成推荐
     */
    private Recommendation generateRecommendation(String userId, String sessionId) throws Exception {
        // 获取用户交互过的商品
        Map<String, Integer> interactions = new HashMap<>();
        for (Map.Entry<String, Integer> entry : userProductInteractions.entries()) {
            interactions.put(entry.getKey(), entry.getValue());
        }
        
        if (interactions.isEmpty()) {
            return null;
        }
        
        // 获取用户类别偏好
        Map<String, Double> preferences = new HashMap<>();
        for (Map.Entry<String, Double> entry : userCategoryPreferences.entries()) {
            preferences.put(entry.getKey(), entry.getValue());
        }
        
        // 基于协同过滤的简单推荐算法
        List<String> recommendedProducts = generateCollaborativeFilteringRecommendations(
                interactions, preferences);
        
        if (recommendedProducts.isEmpty()) {
            // 如果协同过滤没有结果，使用基于内容的推荐
            recommendedProducts = generateContentBasedRecommendations(preferences);
        }
        
        // 计算置信度
        double confidence = calculateConfidence(interactions, recommendedProducts);
        
        // 生成推荐原因
        String reason = generateRecommendationReason(interactions, preferences);
        
        return new Recommendation(
                userId,
                recommendedProducts,
                "hybrid_recommendation",
                confidence,
                sessionId,
                reason
        );
    }
    
    /**
     * 基于协同过滤的推荐
     */
    private List<String> generateCollaborativeFilteringRecommendations(
            Map<String, Integer> interactions, Map<String, Double> preferences) {
        
        // 简化的协同过滤：基于用户最喜欢的商品推荐相似商品
        List<String> topProducts = interactions.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        // 模拟相似商品推荐（实际应用中需要商品相似度矩阵）
        List<String> recommendations = new ArrayList<>();
        for (String productId : topProducts) {
            // 生成相似商品ID（简化实现）
            String similarProduct1 = "similar_" + productId + "_1";
            String similarProduct2 = "similar_" + productId + "_2";
            
            if (!interactions.containsKey(similarProduct1)) {
                recommendations.add(similarProduct1);
            }
            if (!interactions.containsKey(similarProduct2)) {
                recommendations.add(similarProduct2);
            }
            
            if (recommendations.size() >= RECOMMENDATION_COUNT) {
                break;
            }
        }
        
        return recommendations.stream().limit(RECOMMENDATION_COUNT).collect(Collectors.toList());
    }
    
    /**
     * 基于内容的推荐
     */
    private List<String> generateContentBasedRecommendations(Map<String, Double> preferences) {
        List<String> recommendations = new ArrayList<>();
        
        // 基于用户偏好的类别推荐商品
        List<String> topCategories = preferences.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(2)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        
        for (String category : topCategories) {
            // 为每个偏好类别生成推荐商品（简化实现）
            for (int i = 1; i <= 3; i++) {
                String productId = "rec_" + category + "_" + i;
                recommendations.add(productId);
                
                if (recommendations.size() >= RECOMMENDATION_COUNT) {
                    break;
                }
            }
            
            if (recommendations.size() >= RECOMMENDATION_COUNT) {
                break;
            }
        }
        
        return recommendations.stream().limit(RECOMMENDATION_COUNT).collect(Collectors.toList());
    }
    
    /**
     * 计算推荐置信度
     */
    private double calculateConfidence(Map<String, Integer> interactions, List<String> recommendations) {
        if (interactions.isEmpty() || recommendations.isEmpty()) {
            return 0.0;
        }
        
        // 基于用户交互次数计算置信度
        int totalInteractions = interactions.values().stream().mapToInt(Integer::intValue).sum();
        double confidence = Math.min(1.0, totalInteractions / 100.0); // 归一化到0-1
        
        return Math.round(confidence * 100.0) / 100.0; // 保留两位小数
    }
    
    /**
     * 生成推荐原因
     */
    private String generateRecommendationReason(Map<String, Integer> interactions, 
                                               Map<String, Double> preferences) {
        if (!preferences.isEmpty()) {
            String topCategory = preferences.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse("unknown");
            return "基于您对 " + topCategory + " 类商品的偏好";
        } else if (!interactions.isEmpty()) {
            return "基于您的浏览和购买历史";
        } else {
            return "为您推荐热门商品";
        }
    }
}

package com.distributed.system.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 推荐结果模型
 */
public class Recommendation implements Serializable {
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("recommendedProducts")
    private List<String> recommendedProducts;
    
    @JsonProperty("algorithm")
    private String algorithm;
    
    @JsonProperty("confidence")
    private Double confidence;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("sessionId")
    private String sessionId;
    
    @JsonProperty("reason")
    private String reason;
    
    // 默认构造函数
    public Recommendation() {}
    
    public Recommendation(String userId, List<String> recommendedProducts, String algorithm, 
                         Double confidence, String sessionId, String reason) {
        this.userId = userId;
        this.recommendedProducts = recommendedProducts;
        this.algorithm = algorithm;
        this.confidence = confidence;
        this.sessionId = sessionId;
        this.reason = reason;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getter和Setter方法
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public List<String> getRecommendedProducts() {
        return recommendedProducts;
    }
    
    public void setRecommendedProducts(List<String> recommendedProducts) {
        this.recommendedProducts = recommendedProducts;
    }
    
    public String getAlgorithm() {
        return algorithm;
    }
    
    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }
    
    public Double getConfidence() {
        return confidence;
    }
    
    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSessionId() {
        return sessionId;
    }
    
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Recommendation that = (Recommendation) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(timestamp, that.timestamp) &&
                Objects.equals(sessionId, that.sessionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, timestamp, sessionId);
    }
    
    @Override
    public String toString() {
        return "Recommendation{" +
                "userId='" + userId + '\'' +
                ", recommendedProducts=" + recommendedProducts +
                ", algorithm='" + algorithm + '\'' +
                ", confidence=" + confidence +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}

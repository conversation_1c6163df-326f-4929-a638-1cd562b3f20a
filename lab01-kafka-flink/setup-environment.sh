#!/bin/bash

# 分布式实时电商推荐系统环境配置脚本
# 适用于Ubuntu 24.04/22.04/20.04

set -e

echo "=== 分布式实时电商推荐系统环境配置 ==="

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "无法检测操作系统版本"
    exit 1
fi

echo "检测到操作系统: $OS $VER"

# 创建工作目录
WORK_DIR="/opt/distributed-system"
sudo mkdir -p $WORK_DIR
sudo chown $USER:$USER $WORK_DIR

# 检查并设置JAVA_HOME
if [ -d "/usr/lib/jvm/java-11-openjdk-amd64" ]; then
    JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
elif [ -d "/usr/lib/jvm/java-8-openjdk-amd64" ]; then
    JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
elif [ -d "/usr/lib/jvm/default-java" ]; then
    JAVA_HOME="/usr/lib/jvm/default-java"
else
    JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"  # 默认值
fi

# 设置环境变量
export JAVA_HOME="$JAVA_HOME"
export KAFKA_HOME="$WORK_DIR/kafka"
export FLINK_HOME="$WORK_DIR/flink"
export PATH=$PATH:$KAFKA_HOME/bin:$FLINK_HOME/bin

# 检查并清理重复的环境变量
grep -v "JAVA_HOME\|KAFKA_HOME\|FLINK_HOME" ~/.bashrc > ~/.bashrc.tmp || true
mv ~/.bashrc.tmp ~/.bashrc

# 将环境变量写入bashrc
echo "" >> ~/.bashrc
echo "# 分布式系统实验环境变量" >> ~/.bashrc
echo "export JAVA_HOME=$JAVA_HOME" >> ~/.bashrc
echo "export KAFKA_HOME=$KAFKA_HOME" >> ~/.bashrc
echo "export FLINK_HOME=$FLINK_HOME" >> ~/.bashrc
echo "export PATH=\$PATH:\$KAFKA_HOME/bin:\$FLINK_HOME/bin" >> ~/.bashrc

# 安装Java和必要工具
echo "安装Java和必要工具..."
if [[ "$OS" == *"Ubuntu"* ]]; then
    sudo apt update
    # Ubuntu 24.04默认使用Java 11，也兼容Java 8
    sudo apt install -y openjdk-11-jdk maven wget curl netcat-openbsd
    # 如果需要Java 8，取消下面的注释
    # sudo apt install -y openjdk-8-jdk
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    sudo yum update -y
    sudo yum install -y java-11-openjdk java-11-openjdk-devel maven wget curl nc
fi

# 验证Java安装
java -version
if [ $? -ne 0 ]; then
    echo "Java安装失败"
    exit 1
fi

# 下载并安装Kafka 3.7.2
echo "下载并安装Kafka 3.7.2..."
cd $WORK_DIR
if [ ! -d "kafka" ]; then
    echo "正在下载Kafka..."
    wget -q --show-progress https://archive.apache.org/dist/kafka/3.7.2/kafka_2.12-3.7.2.tgz
    echo "正在解压Kafka..."
    tar -xzf kafka_2.12-3.7.2.tgz
    mv kafka_2.12-3.7.2 kafka
    rm kafka_2.12-3.7.2.tgz
    echo "Kafka安装完成"
fi

# 下载并安装Flink 1.17.2
echo "下载并安装Flink 1.17.2..."
if [ ! -d "flink" ]; then
    echo "正在下载Flink..."
    wget -q --show-progress https://archive.apache.org/dist/flink/flink-1.17.2/flink-1.17.2-bin-scala_2.12.tgz
    echo "正在解压Flink..."
    tar -xzf flink-1.17.2-bin-scala_2.12.tgz
    mv flink-1.17.2 flink
    rm flink-1.17.2-bin-scala_2.12.tgz
    echo "Flink安装完成"
fi

# 创建数据目录
sudo mkdir -p /var/lib/kafka-logs
sudo mkdir -p /var/lib/zookeeper
sudo chown -R $USER:$USER /var/lib/kafka-logs /var/lib/zookeeper

# 配置本地主机名映射（单机模式）
echo "配置主机名映射..."
# 检查是否已经配置过
if ! grep -q "hadoop01" /etc/hosts; then
    sudo tee -a /etc/hosts << EOF

# 分布式系统实验节点（单机模式）
127.0.0.1 hadoop01
127.0.0.1 hadoop02
127.0.0.1 hadoop03
EOF
fi

echo "环境配置完成！"
echo "请运行 'source ~/.bashrc' 来加载环境变量"
echo "然后可以开始配置Kafka和Flink集群"

#!/bin/bash

# 分布式实时电商推荐系统环境配置脚本
# 适用于Ubuntu 20.04/CentOS 7

set -e

echo "=== 分布式实时电商推荐系统环境配置 ==="

# 检测操作系统
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "无法检测操作系统版本"
    exit 1
fi

echo "检测到操作系统: $OS $VER"

# 创建工作目录
WORK_DIR="/opt/distributed-system"
sudo mkdir -p $WORK_DIR
sudo chown $USER:$USER $WORK_DIR

# 设置环境变量
export JAVA_HOME="/usr/lib/jvm/java-8-openjdk-amd64"
export KAFKA_HOME="$WORK_DIR/kafka"
export FLINK_HOME="$WORK_DIR/flink"
export PATH=$PATH:$KAFKA_HOME/bin:$FLINK_HOME/bin

# 将环境变量写入bashrc
echo "export JAVA_HOME=$JAVA_HOME" >> ~/.bashrc
echo "export KAFKA_HOME=$KAFKA_HOME" >> ~/.bashrc
echo "export FLINK_HOME=$FLINK_HOME" >> ~/.bashrc
echo "export PATH=\$PATH:\$KAFKA_HOME/bin:\$FLINK_HOME/bin" >> ~/.bashrc

# 安装Java 8
echo "安装Java 8..."
if [[ "$OS" == *"Ubuntu"* ]]; then
    sudo apt update
    sudo apt install -y openjdk-8-jdk wget curl
elif [[ "$OS" == *"CentOS"* ]]; then
    sudo yum update -y
    sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel wget curl
fi

# 验证Java安装
java -version
if [ $? -ne 0 ]; then
    echo "Java安装失败"
    exit 1
fi

# 下载并安装Kafka 3.6.0
echo "下载并安装Kafka 3.6.0..."
cd $WORK_DIR
if [ ! -d "kafka" ]; then
    wget https://downloads.apache.org/kafka/3.6.0/kafka_2.13-3.6.0.tgz
    tar -xzf kafka_2.13-3.6.0.tgz
    mv kafka_2.13-3.6.0 kafka
    rm kafka_2.13-3.6.0.tgz
fi

# 下载并安装Flink 1.17
echo "下载并安装Flink 1.17..."
if [ ! -d "flink" ]; then
    wget https://downloads.apache.org/flink/flink-1.17.2/flink-1.17.2-bin-scala_2.12.tgz
    tar -xzf flink-1.17.2-bin-scala_2.12.tgz
    mv flink-1.17.2 flink
    rm flink-1.17.2-bin-scala_2.12.tgz
fi

# 创建数据目录
sudo mkdir -p /var/lib/kafka-logs
sudo mkdir -p /var/lib/zookeeper
sudo chown -R $USER:$USER /var/lib/kafka-logs /var/lib/zookeeper

# 配置主机名映射（需要根据实际环境修改）
echo "配置主机名映射..."
sudo tee -a /etc/hosts << EOF

# 分布式系统实验节点
************* hadoop01
************* hadoop02  
************* hadoop03
EOF

echo "环境配置完成！"
echo "请运行 'source ~/.bashrc' 来加载环境变量"
echo "然后可以开始配置Kafka和Flink集群"

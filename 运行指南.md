# 分布式系统实验运行指南

本指南将帮助您完成分布式系统实验的环境配置和代码运行。

## 系统要求

### 硬件要求
- **CPU**: 至少2核
- **内存**: 至少8GB
- **存储**: 至少50GB可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (推荐Ubuntu 20.04或CentOS 7)
- **Java**: OpenJDK 8或更高版本
- **Go**: 1.19或更高版本
- **Maven**: 3.6或更高版本

## 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目代码
cd /path/to/distributed_system_experiment

# 检查项目结构
ls -la
# 应该看到：
# lab01-kafka-flink/
# lab02-raft/
# docs/
# README.md
# 运行指南.md
```

### 2. 第一项实验：Kafka + Flink 推荐系统

#### 步骤1：环境配置
```bash
cd lab01-kafka-flink

# 运行环境配置脚本
sudo ./setup-environment.sh

# 加载环境变量
source ~/.bashrc

# 验证安装
java -version
echo $KAFKA_HOME
echo $FLINK_HOME
```

#### 步骤2：启动Kafka集群

**单机模式（推荐用于测试）**:
```bash
cd kafka-cluster
./start-kafka-standalone.sh
```

**集群模式（需要3台机器）**:
```bash
# 在每台机器上运行
cd kafka-cluster
./start-kafka-cluster.sh
```

#### 步骤3：启动Flink集群

**单机模式**:
```bash
cd flink-cluster
./start-flink-standalone.sh
```

**集群模式**:
```bash
cd flink-cluster
./start-flink-cluster.sh
```

#### 步骤4：运行推荐系统
```bash
cd recommendation-system
./run-recommendation-system.sh
```

#### 步骤5：启动消息源软件
```bash
cd message-source
./run-message-source.sh
```

### 3. 第二项实验：Raft一致性协议

```bash
cd lab02-raft

# 运行所有测试
./run-tests.sh

# 运行特定测试
./run-tests.sh -test 2A    # 选举测试
./run-tests.sh -test 2B    # 日志复制测试
./run-tests.sh -test 2C    # 持久化测试
./run-tests.sh -test 2D    # 快照测试

# 详细输出和竞态检测
./run-tests.sh -v -race
```

## 详细配置说明

### Kafka配置

#### 主机名配置
如果使用集群模式，需要配置主机名映射：

```bash
# 编辑 /etc/hosts 文件
sudo vim /etc/hosts

# 添加以下内容（根据实际IP调整）
************* hadoop01
************* hadoop02
************* hadoop03
```

#### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 2181    # ZooKeeper
sudo ufw allow 9092    # Kafka

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=2181/tcp
sudo firewall-cmd --permanent --add-port=9092/tcp
sudo firewall-cmd --reload
```

### Flink配置

#### Web UI访问
- **单机模式**: http://localhost:8081
- **集群模式**: http://hadoop01:8081

#### 内存配置
如果内存不足，可以调整配置：

```bash
# 编辑 flink-conf.yaml
vim flink-cluster/flink-conf.yaml

# 调整以下参数
jobmanager.memory.process.size: 1024m
taskmanager.memory.process.size: 1024m
```

## 故障排除

### 常见问题

#### 1. Java版本问题
```bash
# 检查Java版本
java -version

# 如果版本不对，安装正确版本
sudo apt install openjdk-8-jdk  # Ubuntu
sudo yum install java-1.8.0-openjdk  # CentOS
```

#### 2. 端口占用
```bash
# 检查端口占用
netstat -tulpn | grep :9092
netstat -tulpn | grep :2181

# 杀死占用进程
sudo kill -9 <PID>
```

#### 3. 权限问题
```bash
# 确保脚本有执行权限
chmod +x *.sh

# 确保数据目录权限正确
sudo chown -R $USER:$USER /var/lib/kafka-logs
sudo chown -R $USER:$USER /var/lib/zookeeper
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h

# 调整JVM参数
export KAFKA_HEAP_OPTS="-Xmx512M -Xms512M"
export FLINK_ENV_JAVA_OPTS="-Xmx1024m"
```

### 日志查看

#### Kafka日志
```bash
tail -f $KAFKA_HOME/logs/server.log
tail -f $KAFKA_HOME/logs/zookeeper.log
```

#### Flink日志
```bash
tail -f $FLINK_HOME/log/flink-*-jobmanager-*.log
tail -f $FLINK_HOME/log/flink-*-taskmanager-*.log
```

#### 应用日志
```bash
tail -f lab01-kafka-flink/recommendation-system/logs/recommendation-system.log
tail -f lab01-kafka-flink/message-source/logs/message-source.log
```

## 测试验证

### 第一项实验验证

#### 1. 验证Kafka集群
```bash
# 创建测试主题
$KAFKA_HOME/bin/kafka-topics.sh --create --topic test --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1

# 发送测试消息
echo "Hello Kafka" | $KAFKA_HOME/bin/kafka-console-producer.sh --topic test --bootstrap-server localhost:9092

# 接收测试消息
$KAFKA_HOME/bin/kafka-console-consumer.sh --topic test --from-beginning --bootstrap-server localhost:9092
```

#### 2. 验证Flink集群
```bash
# 提交示例作业
$FLINK_HOME/bin/flink run $FLINK_HOME/examples/streaming/WordCount.jar

# 查看作业状态
$FLINK_HOME/bin/flink list
```

#### 3. 验证推荐系统
- 访问Flink Web UI查看作业运行状态
- 在消息源软件中生成用户行为数据
- 观察推荐结果输出

### 第二项实验验证

#### 运行测试套件
```bash
cd lab02-raft

# 运行基础测试
./run-tests.sh -test 2A -v

# 检查测试结果
echo $?  # 应该返回0表示成功
```

## 性能调优

### Kafka调优
```bash
# 增加分区数提高并行度
$KAFKA_HOME/bin/kafka-topics.sh --alter --topic user-behavior-events --partitions 6 --bootstrap-server localhost:9092

# 调整批处理大小
# 在producer配置中设置
batch.size=32768
linger.ms=5
```

### Flink调优
```bash
# 增加并行度
parallelism.default: 4

# 调整检查点间隔
execution.checkpointing.interval: 30s
```

## 清理环境

### 停止服务
```bash
# 停止Flink
$FLINK_HOME/bin/stop-cluster.sh

# 停止Kafka
$KAFKA_HOME/bin/kafka-server-stop.sh
$KAFKA_HOME/bin/zookeeper-server-stop.sh
```

### 清理数据
```bash
# 清理Kafka数据
sudo rm -rf /var/lib/kafka-logs/*
sudo rm -rf /var/lib/zookeeper/*

# 清理Flink数据
sudo rm -rf /tmp/flink-*
```

## 获取帮助

### 文档资源
- [Kafka官方文档](https://kafka.apache.org/documentation/)
- [Flink官方文档](https://flink.apache.org/docs/)
- [Raft论文](https://raft.github.io/raft.pdf)

### 常用命令参考
```bash
# Kafka命令
$KAFKA_HOME/bin/kafka-topics.sh --help
$KAFKA_HOME/bin/kafka-console-producer.sh --help
$KAFKA_HOME/bin/kafka-console-consumer.sh --help

# Flink命令
$FLINK_HOME/bin/flink --help
$FLINK_HOME/bin/flink run --help

# Go测试命令
go test --help
```

如果遇到问题，请检查日志文件并参考故障排除部分。

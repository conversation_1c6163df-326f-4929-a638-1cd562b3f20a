# 分布式系统实验报告

**学生姓名**: [请填写]  
**学号**: [请填写]  
**班级**: [请填写]  
**实验日期**: [请填写]  

## 实验概述

本实验包含两个主要部分：
1. 基于Kafka和Flink的分布式实时电商推荐系统
2. Raft一致性协议的Go语言实现

## 第一项实验：分布式实时电商推荐系统 (50分)

### 实验环境配置 (15分)

#### Kafka集群配置
- **节点数量**: 3个节点 (hadoop01, hadoop02, hadoop03)
- **Kafka版本**: 3.6.0
- **ZooKeeper配置**: 集群模式
- **主题配置**: 
  - product-events: 3分区，3副本
  - user-behavior-events: 3分区，3副本
  - recommendation-results: 3分区，3副本

**配置截图**:
```
[请在此处插入Kafka集群启动截图]
```

**遇到的问题及解决方案**:
- 问题1: [描述问题]
  - 解决方案: [描述解决方案]
- 问题2: [描述问题]
  - 解决方案: [描述解决方案]

#### Flink集群配置 (15分)
- **JobManager**: hadoop01:8081
- **TaskManager**: 3个节点，每个2个slot
- **Flink版本**: 1.17.2
- **并行度**: 3

**配置截图**:
```
[请在此处插入Flink Web UI截图]
```

### 推荐系统实现 (10分)

#### 算法设计
- **推荐算法**: 混合推荐算法（协同过滤 + 基于内容）
- **实时性**: 30秒内生成推荐
- **推荐数量**: 每次推荐5个商品

#### 核心代码实现
```java
// 请在此处展示关键代码片段
public class RecommendationProcessor extends KeyedProcessFunction<String, UserBehavior, Recommendation> {
    // 核心推荐逻辑
}
```

**运行截图**:
```
[请在此处插入推荐系统运行截图]
```

### 消息源软件 (10分)

#### 功能实现
- **用户行为模拟**: 支持view、click、add_to_cart、purchase四种行为
- **商品事件生成**: 自动生成商品信息
- **批量模拟**: 支持批量生成用户行为数据
- **实时监控**: 实时显示推荐结果

#### 运行演示
```
[请在此处插入消息源软件运行截图]
```

**测试数据**:
- 模拟用户数: [填写数量]
- 生成行为数: [填写数量]
- 推荐响应时间: [填写时间]

### 系统性能测试

#### 吞吐量测试
- **消息生产速率**: [填写数据] 条/秒
- **消息消费速率**: [填写数据] 条/秒
- **推荐生成速率**: [填写数据] 次/秒

#### 延迟测试
- **端到端延迟**: [填写数据] 毫秒
- **推荐响应时间**: [填写数据] 毫秒

## 第二项实验：Raft一致性协议 (40分)

### Raft选举实现 (10分)

#### 选举机制
- **选举超时**: 150-300ms随机
- **心跳间隔**: 50ms
- **投票规则**: 实现日志新旧比较

#### 测试结果
```bash
# 运行选举测试
./run-tests.sh -test 2A -v

[请在此处粘贴测试输出]
```

**测试通过情况**:
- TestInitialElection2A: ✅/❌
- TestReElection2A: ✅/❌
- TestManyElections2A: ✅/❌

### Raft日志复制 (10分)

#### 日志复制机制
- **日志结构**: Term + Index + Command
- **一致性检查**: PrevLogTerm和PrevLogIndex
- **冲突解决**: 快速回退算法

#### 测试结果
```bash
# 运行日志复制测试
./run-tests.sh -test 2B -v

[请在此处粘贴测试输出]
```

**测试通过情况**:
- TestBasicAgree2B: ✅/❌
- TestRPCBytes2B: ✅/❌
- TestFollowerFailure2B: ✅/❌
- TestLeaderFailure2B: ✅/❌
- TestFailAgree2B: ✅/❌
- TestFailNoAgree2B: ✅/❌
- TestConcurrentStarts2B: ✅/❌
- TestRejoin2B: ✅/❌
- TestBackup2B: ✅/❌
- TestCount2B: ✅/❌

### Raft持久化 (10分)

#### 持久化实现
- **持久化状态**: currentTerm, votedFor, log
- **存储格式**: Gob编码
- **崩溃恢复**: 从持久化状态恢复

#### 测试结果
```bash
# 运行持久化测试
./run-tests.sh -test 2C -v

[请在此处粘贴测试输出]
```

### Raft日志压缩 (10分)

#### 快照机制
- **快照触发**: 日志大小超过阈值
- **快照内容**: 状态机状态 + 最后包含的索引和任期
- **InstallSnapshot RPC**: 传输快照给落后节点

#### 测试结果
```bash
# 运行快照测试
./run-tests.sh -test 2D -v

[请在此处粘贴测试输出]
```

## 实验总结

### 技术收获
1. **分布式系统理解**: [描述对分布式系统的理解]
2. **实时流处理**: [描述对Kafka和Flink的理解]
3. **一致性算法**: [描述对Raft算法的理解]
4. **系统设计**: [描述系统设计的经验]

### 遇到的挑战
1. **挑战1**: [描述挑战]
   - **解决方案**: [描述解决方案]
2. **挑战2**: [描述挑战]
   - **解决方案**: [描述解决方案]

### 改进建议
1. [改进建议1]
2. [改进建议2]
3. [改进建议3]

### 实验心得
[请在此处写下实验心得体会，不少于200字]

## 附录

### 代码结构
```
distributed_system_experiment/
├── lab01-kafka-flink/          # 第一项实验
│   ├── kafka-cluster/          # Kafka集群配置
│   ├── flink-cluster/          # Flink集群配置
│   ├── recommendation-system/  # 推荐系统实现
│   └── message-source/         # 消息源软件
├── lab02-raft/                 # 第二项实验
│   ├── src/                    # Raft实现源码
│   ├── test/                   # 测试用例
│   └── docs/                   # 文档
└── docs/                       # 实验报告和文档
```

### 运行环境
- **操作系统**: [填写操作系统版本]
- **Java版本**: [填写Java版本]
- **Go版本**: [填写Go版本]
- **硬件配置**: [填写硬件配置]

### 参考资料
1. Apache Kafka官方文档
2. Apache Flink官方文档
3. Raft论文: "In Search of an Understandable Consensus Algorithm"
4. MIT 6.824分布式系统课程
5. [其他参考资料]

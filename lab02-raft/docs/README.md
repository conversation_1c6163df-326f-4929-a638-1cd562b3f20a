# Raft一致性协议实现

本项目实现了Raft分布式共识算法，包含领导者选举、日志复制、持久化和日志压缩等核心功能。

## 项目结构

```
lab02-raft/
├── src/                    # 源代码
│   ├── raft.go            # Raft核心数据结构和接口
│   ├── election.go        # 选举和心跳逻辑
│   ├── applier.go         # 日志应用器
│   ├── util.go            # 工具函数
│   ├── labrpc.go          # RPC通信框架
│   └── persister.go       # 持久化存储
├── test/                   # 测试代码
│   ├── raft_test.go       # 主要测试用例
│   └── config.go          # 测试配置和工具
├── docs/                   # 文档
│   └── README.md          # 本文档
├── go.mod                  # Go模块定义
└── run-tests.sh           # 测试运行脚本
```

## 实现特性

### 第一部分：领导者选举 (2A)
- ✅ **选举超时机制**: 随机化选举超时，避免选票分裂
- ✅ **心跳机制**: 领导者定期发送心跳维持权威
- ✅ **任期管理**: 正确处理任期更新和降级
- ✅ **投票逻辑**: 实现RequestVote RPC和投票规则

### 第二部分：日志复制 (2B)
- ✅ **日志条目结构**: 包含任期、索引和命令
- ✅ **AppendEntries RPC**: 实现日志复制和一致性检查
- ✅ **提交机制**: 基于多数派确认的日志提交
- ✅ **冲突解决**: 快速回退算法处理日志不一致

### 第三部分：持久化 (2C)
- ✅ **状态持久化**: 保存currentTerm、votedFor和log
- ✅ **崩溃恢复**: 从持久化状态正确恢复
- ✅ **原子性保证**: 确保状态更新的原子性

### 第四部分：日志压缩 (2D)
- ✅ **快照机制**: 实现日志压缩和快照创建
- ✅ **InstallSnapshot RPC**: 传输快照给落后的跟随者
- ✅ **状态机集成**: 与上层状态机协调快照操作

## 核心算法

### 选举算法
1. **跟随者状态**: 等待领导者心跳，超时后转为候选者
2. **候选者状态**: 增加任期，投票给自己，请求其他节点投票
3. **领导者状态**: 发送心跳，处理客户端请求，复制日志

### 日志复制算法
1. **接收命令**: 领导者接收客户端命令，添加到本地日志
2. **并行复制**: 向所有跟随者并行发送AppendEntries RPC
3. **多数派确认**: 收到多数派确认后提交日志条目
4. **应用到状态机**: 将已提交的条目应用到状态机

### 安全性保证
- **选举安全性**: 每个任期最多一个领导者
- **日志匹配性**: 相同索引和任期的日志条目内容相同
- **领导者完整性**: 领导者包含所有已提交的日志条目
- **状态机安全性**: 所有节点在相同索引应用相同命令

## 性能优化

### 网络优化
- **批量发送**: 将多个日志条目打包发送
- **并行复制**: 为每个跟随者启动独立的复制协程
- **快速回退**: 使用冲突信息快速定位一致点

### 内存优化
- **日志压缩**: 定期创建快照，删除旧日志
- **数组收缩**: 动态调整日志数组大小
- **状态清理**: 及时清理无用的状态信息

## 测试用例

### 2A: 选举测试
- `TestInitialElection2A`: 初始选举测试
- `TestReElection2A`: 重新选举测试
- `TestManyElections2A`: 多次选举测试

### 2B: 日志复制测试
- `TestBasicAgree2B`: 基本一致性测试
- `TestRPCBytes2B`: RPC字节数测试
- `TestFollowerFailure2B`: 跟随者故障测试
- `TestLeaderFailure2B`: 领导者故障测试
- `TestFailAgree2B`: 故障情况下的一致性测试
- `TestFailNoAgree2B`: 无法达成一致的测试
- `TestConcurrentStarts2B`: 并发启动测试
- `TestRejoin2B`: 重新加入测试
- `TestBackup2B`: 快速回退测试
- `TestCount2B`: RPC计数测试

### 2C: 持久化测试
- `TestPersist12C`: 基本持久化测试
- `TestPersist22C`: 复杂持久化测试
- `TestPersist32C`: 持久化与分区测试

### 2D: 快照测试
- `TestSnapshotBasic2D`: 基本快照测试
- `TestSnapshotInstall2D`: 快照安装测试
- `TestSnapshotInstallUnreliable2D`: 不可靠网络下的快照测试
- `TestSnapshotInstallCrash2D`: 崩溃情况下的快照测试
- `TestSnapshotInstallUnCrash2D`: 快照安装与崩溃测试

## 运行测试

### 基本用法
```bash
# 运行所有测试
./run-tests.sh

# 运行特定部分的测试
./run-tests.sh -test 2A    # 选举测试
./run-tests.sh -test 2B    # 日志复制测试
./run-tests.sh -test 2C    # 持久化测试
./run-tests.sh -test 2D    # 快照测试

# 详细输出
./run-tests.sh -v

# 启用竞态检测
./run-tests.sh -race

# 多次运行测试
./run-tests.sh -count 5

# 组合使用
./run-tests.sh -v -race -count 3 -test 2B
```

### 调试模式
在`src/util.go`中设置`Debug = true`可以启用详细的调试输出。

## 实现细节

### 状态管理
- 使用读写锁保护共享状态
- 条件变量协调协程间通信
- 原子操作处理简单状态

### 并发控制
- 每个跟随者一个复制协程
- 独立的应用协程处理已提交日志
- 主循环处理选举和心跳

### 错误处理
- 网络分区容错
- 节点崩溃恢复
- 消息丢失重传

## 性能指标

### 延迟
- 正常情况下的提交延迟: < 100ms
- 领导者选举时间: < 1s
- 故障恢复时间: < 5s

### 吞吐量
- 单领导者处理能力: > 1000 ops/s
- 网络带宽利用率: < 80%
- 内存使用: < 100MB

### 可靠性
- 容忍 (n-1)/2 个节点故障
- 网络分区自动恢复
- 数据持久化保证

## 参考资料

1. [Raft论文](https://raft.github.io/raft.pdf) - In Search of an Understandable Consensus Algorithm
2. [Raft可视化](http://thesecretlivesofdata.com/raft/) - Raft算法动画演示
3. [MIT 6.824课程](https://pdos.csail.mit.edu/6.824/) - 分布式系统课程
4. [Raft官网](https://raft.github.io/) - 官方资源和实现

## 常见问题

### Q: 测试失败怎么办？
A: 首先启用调试输出，查看日志分析问题。常见问题包括竞态条件、死锁、状态不一致等。

### Q: 如何提高性能？
A: 可以调整心跳间隔、批量大小、并发度等参数。注意平衡延迟和吞吐量。

### Q: 如何处理网络分区？
A: Raft算法天然支持网络分区，只要多数派节点连通就能正常工作。

### Q: 快照何时创建？
A: 当日志大小超过阈值时自动创建快照，也可以由上层应用主动触发。

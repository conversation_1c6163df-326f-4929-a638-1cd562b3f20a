#!/bin/bash

# Raft一致性协议测试运行脚本

set -e

echo "=== Raft一致性协议测试 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "错误: Go语言环境未安装"
    echo "请安装Go 1.19或更高版本"
    exit 1
fi

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo "Go版本: $GO_VERSION"

# 进入项目目录
cd "$(dirname "$0")"

# 初始化Go模块（如果需要）
if [ ! -f "go.sum" ]; then
    echo "初始化Go模块..."
    go mod tidy
fi

# 解析命令行参数
TEST_PATTERN=""
VERBOSE=""
COUNT=1
RACE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -v|--verbose)
            VERBOSE="-v"
            shift
            ;;
        -race|--race)
            RACE="-race"
            shift
            ;;
        -count)
            COUNT="$2"
            shift 2
            ;;
        -test)
            TEST_PATTERN="$2"
            shift 2
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  -v, --verbose     详细输出"
            echo "  -race, --race     启用竞态检测"
            echo "  -count N          运行测试N次"
            echo "  -test PATTERN     运行匹配模式的测试"
            echo "  -h, --help        显示帮助信息"
            echo ""
            echo "可用的测试模式:"
            echo "  2A                选举测试"
            echo "  2B                日志复制测试"
            echo "  2C                持久化测试"
            echo "  2D                快照测试"
            echo ""
            echo "示例:"
            echo "  $0                    # 运行所有测试"
            echo "  $0 -test 2A           # 只运行选举测试"
            echo "  $0 -v -race           # 详细输出并启用竞态检测"
            echo "  $0 -count 5 -test 2B  # 运行日志复制测试5次"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 构建测试参数
TEST_ARGS="$VERBOSE $RACE -count=$COUNT"

if [ -n "$TEST_PATTERN" ]; then
    case $TEST_PATTERN in
        "2A")
            TEST_ARGS="$TEST_ARGS -run Test.*2A"
            echo "运行选举测试 (2A)..."
            ;;
        "2B")
            TEST_ARGS="$TEST_ARGS -run Test.*2B"
            echo "运行日志复制测试 (2B)..."
            ;;
        "2C")
            TEST_ARGS="$TEST_ARGS -run Test.*2C"
            echo "运行持久化测试 (2C)..."
            ;;
        "2D")
            TEST_ARGS="$TEST_ARGS -run Test.*2D"
            echo "运行快照测试 (2D)..."
            ;;
        *)
            TEST_ARGS="$TEST_ARGS -run $TEST_PATTERN"
            echo "运行匹配 '$TEST_PATTERN' 的测试..."
            ;;
    esac
else
    echo "运行所有Raft测试..."
fi

echo "测试参数: $TEST_ARGS"
echo "开始时间: $(date)"
echo ""

# 运行测试
cd test
if go test $TEST_ARGS ./...; then
    echo ""
    echo "✅ 所有测试通过!"
    echo "结束时间: $(date)"
else
    echo ""
    echo "❌ 测试失败!"
    echo "结束时间: $(date)"
    exit 1
fi
